import type { AppRouteModule } from '/@/router/types';
import { LAYOUT } from '/@/router/constant';

const teacherExam: AppRouteModule = {
  path: '/teacher-exam',
  name: 'TeacherExam',
  component: LAYOUT,
  redirect: '/teacher-exam/review',
  meta: {
    orderNo: 200,
    icon: 'ion:document-text-outline',
    title: '教师考试',
  },
  children: [
    {
      path: 'review',
      name: 'TeacherExamReview',
      component: () => import('/@/views/students_exam/teacher/TeacherExamReviewList.vue'),
      meta: {
        title: '试卷批改',
        icon: 'ion:checkmark-done-outline',
      },
    },
    {
      path: 'test-simple',
      name: 'SimpleTest',
      component: () => import('/@/views/students_exam/teacher/SimpleTestPage.vue'),
      meta: {
        title: '简单测试',
        hideMenu: true,
      },
    },
    {
      path: 'review/:paperId',
      name: 'TeacherReviewDrawer',
      component: () => import('/@/views/students_exam/teacher/SimpleTestPage.vue'),
      meta: {
        title: '试卷批改',
        hideMenu: true,
      },
    },
  ],
};

export default teacherExam; 