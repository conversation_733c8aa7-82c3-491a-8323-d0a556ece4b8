{"mcpServers": {"mysql": {"command": "uvx", "args": ["--from", "mysql-mcp-server", "mysql_mcp_server"], "env": {"MYSQL_HOST": "localhost", "MYSQL_USER": "root", "MYSQL_PASSWORD": "123456", "MYSQL_DATABASE": "jeecg-boot"}, "disabled": false, "alwaysAllow": ["execute_sql"]}, "context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp"], "env": {"DEFAULT_MINIMUM_TOKENS": ""}}, "sequentialthinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "C:/Projects/Jeecg"]}, "git": {"command": "uvx", "args": ["mcp-server-git", "--repository", "C:/Projects/Jeecg"]}, "browser-tools": {"command": "cmd", "args": ["/c", "npx", "-y", "@agentdeskai/browser-tools-mcp@1.2.0"], "enabled": true, "autoApprove": ["takeScreenshot"]}, "playwright": {"command": "cmd", "args": ["/c", "npx", "-y", "@executeautomation/playwright-mcp-server"], "enabled": true, "autoApprove": [], "env": {"PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD": "false"}}}}