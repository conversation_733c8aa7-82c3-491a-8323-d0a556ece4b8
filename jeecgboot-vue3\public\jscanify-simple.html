<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JScanify - Simple Document Scanner</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .upload-area {
            border: 2px dashed #007bff;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            background: #f8f9ff;
        }
        .upload-area:hover {
            background: #e6f3ff;
        }

        .upload-area {
            transition: all 0.3s ease;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .results {
            margin-top: 30px;
            display: none;
        }
        .result-item {
            margin: 20px 0;
            text-align: center;
        }
        .result-canvas {
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        #fileInput {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📄 JScanify Document Scanner</h1>
        <p>Upload a photo and automatically detect and crop documents</p>
        
        <div id="status" class="status info">Initializing scanner...</div>
        
        <div class="upload-area" id="uploadArea">
            <h3>📁 Click to Upload or Drag & Drop Image</h3>
            <p>Supports JPG, PNG, GIF files</p>
            <input type="file" id="fileInput" accept="image/*">
        </div>
        
        <div>
            <button class="btn" onclick="document.getElementById('fileInput').click()">Choose File</button>
            <button class="btn" id="scanBtn" onclick="scanDocument()" disabled>Scan Document</button>
            <button class="btn" id="downloadBtn" onclick="downloadResult()" disabled style="display: none;">Download Result</button>
        </div>
        
        <div class="results" id="results">
            <h2>Scan Results</h2>
            <div class="result-item">
                <h3>Original with Detection</h3>
                <canvas id="highlightCanvas" class="result-canvas"></canvas>
            </div>
            <div class="result-item">
                <h3>Extracted Document</h3>
                <canvas id="extractCanvas" class="result-canvas"></canvas>
            </div>
        </div>
    </div>

    <script>
        let scanner;
        let currentImage;
        let isReady = false;
        let extractedCanvas;

        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            console.log(message);
            
            // 向父窗口发送状态更新消息
            if (window.parent !== window) {
                window.parent.postMessage({
                    type: 'STATUS_UPDATE',
                    status: message,
                    statusType: type
                }, '*');
            }
        }

        // Initialize scanner
        function initializeScanner() {
            updateStatus('Loading OpenCV...', 'info');
            
            // Load OpenCV
            const script = document.createElement('script');
            script.src = 'https://docs.opencv.org/4.7.0/opencv.js';
            script.async = true;
            
            script.onload = () => {
                updateStatus('OpenCV loaded, initializing...', 'info');
                
                const checkCV = () => {
                    if (typeof cv !== 'undefined' && cv.Mat) {
                        // Load JScanify
                        const jscanifyScript = document.createElement('script');
                        jscanifyScript.src = 'https://cdn.jsdelivr.net/gh/ColonelParrot/jscanify@master/src/jscanify.min.js';
                        jscanifyScript.onload = () => {
                            try {
                                scanner = new jscanify();
                                isReady = true;
                                updateStatus('✅ Scanner ready! Upload an image to get started.', 'success');
                            } catch (error) {
                                updateStatus('❌ Failed to initialize scanner: ' + error.message, 'error');
                            }
                        };
                        jscanifyScript.onerror = () => {
                            updateStatus('❌ Failed to load JScanify library', 'error');
                        };
                        document.head.appendChild(jscanifyScript);
                    } else {
                        setTimeout(checkCV, 100);
                    }
                };
                checkCV();
            };
            
            script.onerror = () => {
                updateStatus('❌ Failed to load OpenCV library', 'error');
            };
            
            document.head.appendChild(script);
        }

        // File handling
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');

        // Click to upload
        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            e.stopPropagation();
            uploadArea.style.background = '#e6f3ff';
            uploadArea.style.transform = 'scale(1.02)';
        });

        uploadArea.addEventListener('dragenter', (e) => {
            e.preventDefault();
            e.stopPropagation();
        });

        uploadArea.addEventListener('dragleave', (e) => {
            e.preventDefault();
            e.stopPropagation();
            if (!uploadArea.contains(e.relatedTarget)) {
                uploadArea.style.background = '#f8f9ff';
                uploadArea.style.transform = 'scale(1)';
            }
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            e.stopPropagation();
            uploadArea.style.background = '#f8f9ff';
            uploadArea.style.transform = 'scale(1)';

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileUpload(files[0]);
            }
        });

        fileInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                handleFileUpload(file);
            }
        });

        function handleFileUpload(file) {
            if (file && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const img = new Image();
                    img.onload = function() {
                        currentImage = img;
                        document.getElementById('scanBtn').disabled = false;
                        updateStatus('✅ Image loaded! Click "Scan Document" to process.', 'success');

                        // Update upload area text
                        uploadArea.innerHTML = `
                            <h3>✅ Image Loaded: ${file.name}</h3>
                            <p>Ready to scan • Click to change or drag new file</p>
                            <input type="file" id="fileInput" accept="image/*" style="display: none;">
                        `;

                        // Re-attach event listeners
                        const newFileInput = document.getElementById('fileInput');
                        newFileInput.addEventListener('change', function(e) {
                            const file = e.target.files[0];
                            if (file) {
                                handleFileUpload(file);
                            }
                        });
                    };
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            } else {
                updateStatus('❌ Please select a valid image file.', 'error');
            }
        }

        function scanDocument() {
            if (!isReady) {
                updateStatus('❌ Scanner not ready yet. Please wait.', 'error');
                return;
            }
            
            if (!currentImage) {
                updateStatus('❌ Please select an image first.', 'error');
                return;
            }

            updateStatus('🔄 Processing document...', 'info');

            try {
                // Highlight paper detection
                const highlightCanvas = scanner.highlightPaper(currentImage);
                const highlightCtx = document.getElementById('highlightCanvas');
                highlightCtx.width = highlightCanvas.width;
                highlightCtx.height = highlightCanvas.height;
                const ctx = highlightCtx.getContext('2d');
                ctx.drawImage(highlightCanvas, 0, 0);

                // Extract paper
                const paperWidth = 800;
                const paperHeight = 1000;
                extractedCanvas = scanner.extractPaper(currentImage, paperWidth, paperHeight);
                
                const extractCtx = document.getElementById('extractCanvas');
                extractCtx.width = extractedCanvas.width;
                extractCtx.height = extractedCanvas.height;
                const ctx2 = extractCtx.getContext('2d');
                ctx2.drawImage(extractedCanvas, 0, 0);

                // Show results
                document.getElementById('results').style.display = 'block';
                document.getElementById('downloadBtn').style.display = 'inline-block';
                document.getElementById('downloadBtn').disabled = false;
                
                updateStatus('✅ Document scanned successfully!', 'success');
                
                // 向父窗口发送成功消息
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'SCAN_SUCCESS',
                        message: 'Document scanned successfully'
                    }, '*');
                }

            } catch (error) {
                console.error('Scanning error:', error);
                updateStatus('❌ Failed to scan document: ' + error.message, 'error');
                
                // 向父窗口发送错误消息
                if (window.parent !== window) {
                    window.parent.postMessage({
                        type: 'SCAN_ERROR',
                        error: error.message
                    }, '*');
                }
            }
        }

        function downloadResult() {
            if (!extractedCanvas) {
                updateStatus('❌ No scanned document to download.', 'error');
                return;
            }

            try {
                const link = document.createElement('a');
                link.download = 'scanned-document.png';
                link.href = extractedCanvas.toDataURL();
                link.click();
                updateStatus('✅ Document downloaded!', 'success');
            } catch (error) {
                updateStatus('❌ Failed to download: ' + error.message, 'error');
            }
        }

        // Start initialization when page loads
        window.addEventListener('load', initializeScanner);
    </script>
</body>
</html> 