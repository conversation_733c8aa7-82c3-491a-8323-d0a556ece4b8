<template>
  <div class="jscanify-container">
    <div class="header">
      <h1>📄 Document Scanner</h1>
      <p>Upload a photo and automatically detect and crop documents</p>
    </div>
    
    <div class="content">
      <!-- Status Display -->
      <div :class="`status ${status}`" v-if="statusMessage">
        {{ statusMessage }}
      </div>
      
      <!-- Upload Area -->
      <div 
        class="upload-area" 
        @click="handleUploadClick"
        @dragover="handleDragOver"
        @dragenter="handleDragEnter" 
        @dragleave="handleDragLeave"
        @drop="handleDrop"
        ref="uploadArea"
      >
        <h3 v-if="!uploadedImage">📁 Click to Upload or Drag & Drop Image</h3>
        <h3 v-else>✅ Image Loaded: {{ fileName }}</h3>
        <p v-if="!uploadedImage">Supports JPG, PNG, GIF files</p>
        <p v-else>Ready to scan • Click to change or drag new file</p>
        <input type="file" ref="fileInput" accept="image/*" @change="handleFileChange" style="display: none;">
      </div>
      
      <!-- Action Buttons -->
      <div class="action-buttons">
        <button class="btn" @click="handleUploadClick">Choose File</button>
        <button class="btn" @click="scanDocument" :disabled="!isReady || !currentImage">Scan Document</button>
        <button class="btn" @click="downloadResult" :disabled="!extractedCanvas" v-show="extractedCanvas">Download Result</button>
      </div>
      
      <!-- Results -->
      <div class="results" v-show="showResults">
        <h2>Scan Results</h2>
        <div class="result-item">
          <h3>Original with Detection</h3>
          <canvas ref="highlightCanvas" class="result-canvas"></canvas>
        </div>
        <div class="result-item">
          <h3>Extracted Document</h3>
          <canvas ref="extractCanvas" class="result-canvas"></canvas>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

// State variables - exactly like HTML version
const scanner = ref(null)
const currentImage = ref(null)
const isReady = ref(false)
const extractedCanvas = ref(null)
const showResults = ref(false)
const fileName = ref('')

// Vue refs
const fileInput = ref(null)
const uploadArea = ref(null)
const highlightCanvas = ref(null)
const extractCanvas = ref(null)

// Status management
const statusMessage = ref('Initializing scanner...')
const status = ref('info')
const uploadedImage = ref(null)

// 添加初始化状态跟踪
const isInitializing = ref(false)

// 全局变量防止重复加载
if (!window.documentScannerInitialized) {
  window.documentScannerInitialized = false
}

// Update status function - exactly like HTML version
function updateStatus(message, type = 'info') {
  statusMessage.value = message
  status.value = type
  console.log(message)
}

// Initialize scanner - with global duplicate loading prevention
function initializeScanner() {
  // 防止重复初始化
  if (isInitializing.value || isReady.value || window.documentScannerInitialized) {
    console.log('Scanner already initializing or ready, skipping...')
    
    // 如果库已经加载，直接初始化实例
    if (typeof window.cv !== 'undefined' && window.cv.Mat && typeof window.jscanify !== 'undefined') {
      try {
        scanner.value = new window.jscanify()
        isReady.value = true
        updateStatus('✅ Scanner ready! Upload an image to get started.', 'success')
      } catch (error) {
        console.error('Failed to initialize scanner instance:', error)
      }
    }
    return
  }

  // 检查是否已经完全加载
  if (typeof window.cv !== 'undefined' && window.cv.Mat && typeof window.jscanify !== 'undefined') {
    console.log('Libraries already loaded, initializing scanner instance...')
    try {
      scanner.value = new window.jscanify()
      isReady.value = true
      updateStatus('✅ Scanner ready! Upload an image to get started.', 'success')
      return
    } catch (error) {
      console.error('Failed to initialize scanner instance:', error)
    }
  }

  isInitializing.value = true
  window.documentScannerInitialized = true
  updateStatus('Loading OpenCV...', 'info')

  // 检查OpenCV是否已经加载但未完全初始化
  if (typeof window.cv !== 'undefined' && window.cv.Mat) {
    console.log('OpenCV already loaded, loading JScanify...')
    loadJScanifyScript()
    return
  }

  // Load OpenCV
  const script = document.createElement('script')
  script.src = 'https://docs.opencv.org/4.7.0/opencv.js'
  script.async = true

  script.onload = () => {
    updateStatus('OpenCV loaded, initializing...', 'info')

    const checkCV = () => {
      if (typeof window.cv !== 'undefined' && window.cv.Mat) {
        loadJScanifyScript()
      } else {
        setTimeout(checkCV, 100)
      }
    }
    checkCV()
  }

  script.onerror = () => {
    isInitializing.value = false
    window.documentScannerInitialized = false
    updateStatus('❌ Failed to load OpenCV library', 'error')
  }

  document.head.appendChild(script)
}

// 分离JScanify加载逻辑
function loadJScanifyScript() {
  // 检查JScanify是否已经加载
  if (typeof window.jscanify !== 'undefined') {
    console.log('JScanify already loaded, initializing scanner...')
    initializeScannerInstance()
    return
  }

  // 检查是否已有JScanify脚本标签
  const existingJScanifyScript = document.querySelector('script[src*="jscanify"]')
  if (existingJScanifyScript) {
    console.log('JScanify script already exists, waiting for load...')
    // 等待现有脚本加载完成
    const checkJScanify = () => {
      if (typeof window.jscanify !== 'undefined') {
        initializeScannerInstance()
      } else {
        setTimeout(checkJScanify, 100)
      }
    }
    checkJScanify()
    return
  }

  const jscanifyScript = document.createElement('script')
  jscanifyScript.src = 'https://cdn.jsdelivr.net/gh/ColonelParrot/jscanify@master/src/jscanify.min.js'
  jscanifyScript.onload = () => {
    initializeScannerInstance()
  }
  jscanifyScript.onerror = () => {
    isInitializing.value = false
    window.documentScannerInitialized = false
    updateStatus('❌ Failed to load JScanify library', 'error')
  }
  document.head.appendChild(jscanifyScript)
}

// 初始化扫描器实例
function initializeScannerInstance() {
  try {
    scanner.value = new window.jscanify()
    isReady.value = true
    isInitializing.value = false
    updateStatus('✅ Scanner ready! Upload an image to get started.', 'success')
  } catch (error) {
    console.error('Failed to initialize JScanify scanner:', error)
    isInitializing.value = false
    updateStatus('❌ Failed to initialize scanner: ' + error.message, 'error')
  }
}

// File handling - exactly like HTML version
function handleUploadClick() {
  fileInput.value.click()
}

function handleDragOver(e) {
  e.preventDefault()
  e.stopPropagation()
  uploadArea.value.style.background = '#e6f3ff'
  uploadArea.value.style.transform = 'scale(1.02)'
}

function handleDragEnter(e) {
  e.preventDefault()
  e.stopPropagation()
}

function handleDragLeave(e) {
  e.preventDefault()
  e.stopPropagation()
  if (!uploadArea.value.contains(e.relatedTarget)) {
    uploadArea.value.style.background = '#f8f9ff'
    uploadArea.value.style.transform = 'scale(1)'
  }
}

function handleDrop(e) {
  e.preventDefault()
  e.stopPropagation()
  uploadArea.value.style.background = '#f8f9ff'
  uploadArea.value.style.transform = 'scale(1)'

  const files = e.dataTransfer.files
  if (files.length > 0) {
    handleFileUpload(files[0])
  }
}

function handleFileChange(e) {
  const file = e.target.files[0]
  if (file) {
    handleFileUpload(file)
  }
}

function handleFileUpload(file) {
  if (file && file.type.startsWith('image/')) {
    fileName.value = file.name
    const reader = new FileReader()
    reader.onload = function(e) {
      // 只存储数据URL，不创建Image元素 - 完全按照sample code模式
      uploadedImage.value = e.target.result
      currentImage.value = null // 清除之前的图像引用
      updateStatus('✅ Image loaded! Click "Scan Document" to process.', 'success')
    }
    reader.readAsDataURL(file)
  } else {
    updateStatus('❌ Please select a valid image file.', 'error')
  }
}

// Scan document - 完全按照sample code的JSCanify.vue模式
async function scanDocument() {
  if (!isReady.value) {
    updateStatus('❌ Scanner not ready yet. Please wait.', 'error')
    return
  }

  if (!uploadedImage.value) {
    updateStatus('❌ Please select an image first.', 'error')
    return
  }

  updateStatus('🔄 Processing document...', 'info')

  try {
    // 完全按照sample code模式：创建新的Image元素并等待加载
    const img = new Image()

    await new Promise((resolve, reject) => {
      img.onload = resolve
      img.onerror = reject
      img.src = uploadedImage.value
    })

    console.log('🔍 Detecting document boundaries...')

    // Highlight paper detection - 使用新创建的img元素
    const highlightedCanvas = scanner.value.highlightPaper(img)

    // Check if canvas refs are available
    if (!highlightCanvas.value || !extractCanvas.value) {
      throw new Error('Canvas elements not found')
    }

    const highlightCtx = highlightCanvas.value
    highlightCtx.width = highlightedCanvas.width
    highlightCtx.height = highlightedCanvas.height
    const ctx = highlightCtx.getContext('2d')
    ctx.drawImage(highlightedCanvas, 0, 0)
    console.log('✅ Highlight detection completed')

    // Extract paper - 使用新创建的img元素
    console.log('✂️ Extracting document...')
    const paperWidth = 800
    const paperHeight = 1000
    const extractedCanvasResult = scanner.value.extractPaper(img, paperWidth, paperHeight)

    const extractCtx = extractCanvas.value
    extractCtx.width = extractedCanvasResult.width
    extractCtx.height = extractedCanvasResult.height
    const ctx2 = extractCtx.getContext('2d')
    ctx2.drawImage(extractedCanvasResult, 0, 0)
    console.log('✅ Document extraction completed')

    // Store the extracted canvas for download
    extractedCanvas.value = extractedCanvasResult

    // Show results
    showResults.value = true

    updateStatus('✅ Document scanned successfully!', 'success')
  } catch (error) {
    console.error('Scanning error:', error)
    updateStatus('❌ Failed to scan document: ' + error.message, 'error')
  }
}

// Download result - exactly like HTML version
function downloadResult() {
  if (!extractedCanvas.value) {
    updateStatus('❌ No scanned document to download.', 'error')
    return
  }

  try {
    const link = document.createElement('a')
    link.download = 'scanned-document.png'
    link.href = extractedCanvas.value.toDataURL()
    link.click()
    updateStatus('✅ Document downloaded!', 'success')
  } catch (error) {
    updateStatus('❌ Failed to download: ' + error.message, 'error')
  }
}

// Initialize when component mounts
onMounted(() => {
  initializeScanner()
})
</script>

<style scoped>
.jscanify-container {
  max-width: 1200px;
  margin: 0 auto;
  background: white;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
  overflow: hidden;
}

.header {
  background: linear-gradient(135deg, #42b883 0%, #35495e 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.header h1 {
  font-size: 2.5em;
  margin-bottom: 10px;
}

.header p {
  font-size: 1.2em;
  opacity: 0.9;
}

.content {
  padding: 30px;
}

.status {
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.status.info {
  background: #e6f3ff;
  color: #35495e;
}

.status.success {
  background: #d4edda;
  color: #155724;
}

.status.error {
  background: #f8d7da;
  color: #721c24;
}

.upload-area {
  border: 2px dashed #42b883;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  background: #f8fffe;
  transition: all 0.3s;
  margin-bottom: 20px;
}

.upload-area:hover {
  border-color: #369870;
  background: #f0fffe;
}

.action-buttons {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s;
}

.btn:hover {
  transform: translateY(-2px);
}

.results {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
}

.result-item {
  margin-bottom: 30px;
}

.result-item:last-child {
  margin-bottom: 0;
}

.result-item h3 {
  color: #35495e;
  margin-bottom: 15px;
  text-align: center;
  font-size: 1.2em;
}

.result-canvas {
  max-width: 100%;
  max-height: 400px;
  border-radius: 6px;
  box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}
</style>
